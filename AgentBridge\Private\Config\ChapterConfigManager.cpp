// Copyright Epic Games, Inc. All Rights Reserved.

#include "Config/ChapterConfigManager.h"
#include "Config/AgentBridgeConfigSettings.h"
#include "HttpModule.h"
#include "Interfaces/IHttpRequest.h"
#include "Interfaces/IHttpResponse.h"
#include "Dom/JsonObject.h"
#include "Serialization/JsonSerializer.h"
#include "Serialization/JsonReader.h"
#include "JsonObjectConverter.h"
#include "HAL/FileManager.h"
#include "Misc/FileHelper.h"
#include "Misc/Paths.h"

DEFINE_LOG_CATEGORY_STATIC(LogChapterConfigManager, Log, All);

void UChapterConfigManager::Initialize(FSubsystemCollectionBase& Collection)
{
    Super::Initialize(Collection);
    UE_LOG(LogChapterConfigManager, Log, TEXT("ChapterConfigManager Initialized."));
    // Configurations can be loaded explicitly by calling LoadAllChapterConfigurationsRecursive()
    // Or, trigger a default load here if desired:
    // LoadAllChapterConfigurationsRecursive();
}

void UChapterConfigManager::Deinitialize()
{
    UE_LOG(LogChapterConfigManager, Log, TEXT("ChapterConfigManager Deinitialized."));
    // Cancel any pending HTTP requests if necessary
    Super::Deinitialize();
}

const UAgentBridgeConfigSettings* UChapterConfigManager::GetSettings() const
{
    return GetDefault<UAgentBridgeConfigSettings>();
}

void UChapterConfigManager::LoadAllChapterConfigurationsRecursive(const FString& OptionalPathInRepo, int32 MaxRecursionDepth)
{
    RawChapterConfigData.Empty();

    const UAgentBridgeConfigSettings* Settings = GetSettings();
    if (!Settings)
    {
        UE_LOG(LogChapterConfigManager, Error, TEXT("Failed to get AgentBridgeConfigSettings."));
        OnAllChapterConfigsLoaded.Broadcast(false);
        return;
    }

    FString RootPathToLoad = OptionalPathInRepo;
    if (RootPathToLoad.IsEmpty())
    {
        // Use a default chapter-specific path or fall back to the general config path
        RootPathToLoad = TEXT("Chapters/"); // Default chapter config path
        if (RootPathToLoad.IsEmpty())
        {
            RootPathToLoad = Settings->DefaultRootConfigPathInRepo;
        }
    }
    // Ensure path ends with a slash if not empty, for consistency
    if (!RootPathToLoad.IsEmpty() && !RootPathToLoad.EndsWith(TEXT("/")))
    {
        RootPathToLoad += TEXT("/");
    }

    if (Settings->bPreferLocalConfigs || Settings->BackendConfigServiceURL.IsEmpty())
    {
        UE_LOG(LogChapterConfigManager, Log, TEXT("Loading chapter configurations from local directory, starting at relative path: '%s' in '%s'"), *RootPathToLoad, *Settings->LocalConfigDirectoryPath);
        LoadChapterConfigurationsFromLocalRecursive(Settings->LocalConfigDirectoryPath, RootPathToLoad, 0, MaxRecursionDepth);
        // Local loading is synchronous for this example
        OnAllChapterConfigsLoaded.Broadcast(RawChapterConfigData.Num() > 0);
    }
    else
    {
        UE_LOG(LogChapterConfigManager, Log, TEXT("Loading chapter configurations from backend: %s, starting at repo path: '%s'"), *Settings->BackendConfigServiceURL, *RootPathToLoad);
        TSharedRef<int32> InFlightRequests = MakeShared<int32>(0);
        TSharedRef<bool> OverallSuccessFlag = MakeShared<bool>(true); // Assume success until a failure
        FetchChapterFileListRecursive(RootPathToLoad, 0, MaxRecursionDepth, InFlightRequests, OverallSuccessFlag);
    }
}

void UChapterConfigManager::FetchChapterFileList(const FString& PathInRepo, FChapterFileListResponseDelegate ResponseDelegate)
{
    const UAgentBridgeConfigSettings* Settings = GetSettings();
    if (!Settings || Settings->BackendConfigServiceURL.IsEmpty())
    {
        UE_LOG(LogChapterConfigManager, Error, TEXT("BackendConfigServiceURL is not set for FetchChapterFileList."));
        ResponseDelegate.ExecuteIfBound(false, PathInRepo, {}, {});
        return;
    }

    FString EncodedPath = FGenericPlatformHttp::UrlEncode(PathInRepo);
    FString Url = FString::Printf(TEXT("%s/api/v1/configs/list?path=%s"), *Settings->BackendConfigServiceURL, *EncodedPath);

    TSharedRef<IHttpRequest, ESPMode::ThreadSafe> HttpRequest = FHttpModule::Get().CreateRequest();
    HttpRequest->SetVerb(TEXT("GET"));
    HttpRequest->SetURL(Url);
    HttpRequest->SetHeader(TEXT("Content-Type"), TEXT("application/json"));

    if (!Settings->BackendAPIKey.IsEmpty())
    {
        HttpRequest->SetHeader(TEXT("Authorization"), FString::Printf(TEXT("Bearer %s"), *Settings->BackendAPIKey));
    }

    HttpRequest->OnProcessRequestComplete().BindUObject(this, &UChapterConfigManager::OnPublicChapterFileListRequestComplete, ResponseDelegate, PathInRepo);
    HttpRequest->ProcessRequest();

    UE_LOG(LogChapterConfigManager, Log, TEXT("Fetching chapter file list from: %s"), *Url);
}

void UChapterConfigManager::FetchChapterFileListRecursive(const FString& PathInRepo, int32 CurrentDepth, int32 MaxDepth, TSharedRef<int32> InFlightRequests, TSharedRef<bool> OverallSuccessFlag)
{
    if (CurrentDepth > MaxDepth)
    {
        return; // Max depth reached
    }

    const UAgentBridgeConfigSettings* Settings = GetSettings();
    if (!Settings || Settings->BackendConfigServiceURL.IsEmpty())
    {
        UE_LOG(LogChapterConfigManager, Error, TEXT("BackendConfigServiceURL is not set."));
        *OverallSuccessFlag = false;
        if (*InFlightRequests == 0) OnAllChapterConfigsLoaded.Broadcast(*OverallSuccessFlag);
        return;
    }

    (*InFlightRequests)++;

    FString EncodedPath = FGenericPlatformHttp::UrlEncode(PathInRepo);
    FString Url = FString::Printf(TEXT("%s/api/v1/configs/list?path=%s"), *Settings->BackendConfigServiceURL, *EncodedPath);

    TSharedRef<IHttpRequest, ESPMode::ThreadSafe> HttpRequest = FHttpModule::Get().CreateRequest();
    HttpRequest->SetVerb(TEXT("GET"));
    HttpRequest->SetURL(Url);
    HttpRequest->SetHeader(TEXT("Content-Type"), TEXT("application/json"));

    if (!Settings->BackendAPIKey.IsEmpty())
    {
        HttpRequest->SetHeader(TEXT("Authorization"), FString::Printf(TEXT("Bearer %s"), *Settings->BackendAPIKey));
    }

    HttpRequest->OnProcessRequestComplete().BindUObject(this, &UChapterConfigManager::OnChapterFileListRequestComplete, PathInRepo, CurrentDepth, MaxDepth, InFlightRequests, OverallSuccessFlag);
    HttpRequest->ProcessRequest();

    UE_LOG(LogChapterConfigManager, Log, TEXT("Fetching chapter file list from: %s (depth %d/%d)"), *Url, CurrentDepth, MaxDepth);
}

bool UChapterConfigManager::GetChapterConfiguration(const FString& ConfigFilePathInRepo, FChapterConfig& OutConfig) const
{
    return GetConfiguration<FChapterConfig>(ConfigFilePathInRepo, OutConfig);
}

bool UChapterConfigManager::GetChapterConfigurationById(const FString& ChapterId, FChapterConfig& OutConfig) const
{
    // Search through all cached configurations to find one with the matching ChapterId
    for (const TPair<FString, FString>& ConfigPair : RawChapterConfigData)
    {
        FChapterConfig TempConfig;
        if (ParseChapterConfigData(ConfigPair.Value, ConfigPair.Key, TempConfig))
        {
            if (TempConfig.ChapterId.Equals(ChapterId, ESearchCase::IgnoreCase))
            {
                OutConfig = TempConfig;
                return true;
            }
        }
    }

    UE_LOG(LogChapterConfigManager, Warning, TEXT("Chapter configuration with ID '%s' not found in cache."), *ChapterId);
    return false;
}

const TMap<FString, FString>& UChapterConfigManager::GetRawChapterConfigCache() const
{
    return RawChapterConfigData;
}

TMap<FString, FChapterConfig> UChapterConfigManager::GetParsedChapterConfigCache() const
{
    TMap<FString, FChapterConfig> ParsedConfigs;

    // Iterate through all raw JSON configurations
    for (const TPair<FString, FString>& ConfigPair : RawChapterConfigData)
    {
        const FString& ConfigPath = ConfigPair.Key;
        const FString& JsonContent = ConfigPair.Value;

        // Try to parse the JSON as an FChapterConfig
        FChapterConfig ChapterConfig;
        if (ParseChapterConfigData(JsonContent, ConfigPath, ChapterConfig))
        {
            // Add to the parsed map only if parsing was successful
            ParsedConfigs.Add(ConfigPath, ChapterConfig);
        }
        else
        {
            // Log that this config couldn't be parsed as an FChapterConfig
            UE_LOG(LogChapterConfigManager, Verbose, TEXT("Config '%s' could not be parsed as an FChapterConfig."), *ConfigPath);
        }
    }

    return ParsedConfigs;
}

TArray<FString> UChapterConfigManager::GetAvailableChapterIds() const
{
    TArray<FString> ChapterIds;

    // Get all parsed configurations and extract their ChapterIds
    TMap<FString, FChapterConfig> ParsedConfigs = GetParsedChapterConfigCache();
    for (const TPair<FString, FChapterConfig>& ConfigPair : ParsedConfigs)
    {
        if (!ConfigPair.Value.ChapterId.IsEmpty())
        {
            ChapterIds.AddUnique(ConfigPair.Value.ChapterId);
        }
    }

    return ChapterIds;
}

void UChapterConfigManager::OnChapterFileListRequestComplete(FHttpRequestPtr Request, FHttpResponsePtr Response, bool bWasSuccessful, FString PathToList, int32 CurrentDepth, int32 MaxDepth, TSharedRef<int32> InFlightRequests, TSharedRef<bool> OverallSuccessFlag)
{
    (*InFlightRequests)--;

    if (!bWasSuccessful || !Response.IsValid() || Response->GetResponseCode() != 200)
    {
        UE_LOG(LogChapterConfigManager, Error, TEXT("Failed to fetch chapter file list for path '%s'. Code: %d"), *PathToList, Response.IsValid() ? Response->GetResponseCode() : -1);
        *OverallSuccessFlag = false;
        if (*InFlightRequests == 0) OnAllChapterConfigsLoaded.Broadcast(*OverallSuccessFlag);
        return;
    }

    FString JsonString = Response->GetContentAsString();
    TSharedPtr<FJsonObject> JsonObject;
    TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(JsonString);

    if (!FJsonSerializer::Deserialize(Reader, JsonObject) || !JsonObject.IsValid())
    {
        UE_LOG(LogChapterConfigManager, Error, TEXT("Failed to parse chapter file list JSON for path '%s': %s"), *PathToList, *JsonString.Left(200));
        *OverallSuccessFlag = false;
        if (*InFlightRequests == 0) OnAllChapterConfigsLoaded.Broadcast(*OverallSuccessFlag);
        return;
    }

    // Fetch individual files
    const TArray<TSharedPtr<FJsonValue>>* FilesJsonArray;
    if (JsonObject->TryGetArrayField(TEXT("files"), FilesJsonArray))
    {
        for (const TSharedPtr<FJsonValue>& FileValue : *FilesJsonArray)
        {
            FString FileName = FileValue->AsString();
            if (FileName.EndsWith(TEXT(".json")))
            {
                FString FilePathInRepo = PathToList + FileName;

                // Request file content
                const UAgentBridgeConfigSettings* Settings = GetSettings();
                FString EncodedFilePath = FGenericPlatformHttp::UrlEncode(FilePathInRepo);
                FString FileUrl = FString::Printf(TEXT("%s/api/v1/configs/file?path=%s"), *Settings->BackendConfigServiceURL, *EncodedFilePath);

                (*InFlightRequests)++;

                TSharedRef<IHttpRequest, ESPMode::ThreadSafe> FileRequest = FHttpModule::Get().CreateRequest();
                FileRequest->SetVerb(TEXT("GET"));
                FileRequest->SetURL(FileUrl);
                FileRequest->SetHeader(TEXT("Content-Type"), TEXT("application/json"));

                if (!Settings->BackendAPIKey.IsEmpty())
                {
                    FileRequest->SetHeader(TEXT("Authorization"), FString::Printf(TEXT("Bearer %s"), *Settings->BackendAPIKey));
                }

                FileRequest->OnProcessRequestComplete().BindUObject(this, &UChapterConfigManager::OnChapterFileContentRequestComplete, FilePathInRepo, InFlightRequests, OverallSuccessFlag);
                FileRequest->ProcessRequest();

                UE_LOG(LogChapterConfigManager, Log, TEXT("Fetching chapter config file: %s"), *FileUrl);
            }
        }
    }

    // Recursively fetch for subfolders
    const TArray<TSharedPtr<FJsonValue>>* FoldersJsonArray;
    if (JsonObject->TryGetArrayField(TEXT("folders"), FoldersJsonArray))
    {
        for (const TSharedPtr<FJsonValue>& FolderValue : *FoldersJsonArray)
        {
            FString FolderName = FolderValue->AsString();
            if (!FolderName.IsEmpty())
            {
                FString SubFolderPath = PathToList + FolderName + TEXT("/");
                FetchChapterFileListRecursive(SubFolderPath, CurrentDepth + 1, MaxDepth, InFlightRequests, OverallSuccessFlag);
            }
        }
    }

    if (*InFlightRequests == 0) OnAllChapterConfigsLoaded.Broadcast(*OverallSuccessFlag);
}

void UChapterConfigManager::OnChapterFileContentRequestComplete(FHttpRequestPtr Request, FHttpResponsePtr Response, bool bWasSuccessful, FString OriginalFilePath, TSharedRef<int32> InFlightRequests, TSharedRef<bool> OverallSuccessFlag)
{
    (*InFlightRequests)--;

    if (bWasSuccessful && Response.IsValid() && Response->GetResponseCode() == 200)
    {
        FString JsonContent = Response->GetContentAsString();
        RawChapterConfigData.Add(OriginalFilePath, JsonContent);
        UE_LOG(LogChapterConfigManager, Log, TEXT("Successfully fetched and cached chapter config: %s"), *OriginalFilePath);
    }
    else
    {
        UE_LOG(LogChapterConfigManager, Error, TEXT("Failed to fetch chapter config content for '%s'. Code: %d"), *OriginalFilePath, Response.IsValid() ? Response->GetResponseCode() : -1);
        *OverallSuccessFlag = false;
    }

    if (*InFlightRequests == 0) OnAllChapterConfigsLoaded.Broadcast(*OverallSuccessFlag);
}

void UChapterConfigManager::OnPublicChapterFileListRequestComplete(FHttpRequestPtr Request, FHttpResponsePtr Response, bool bWasSuccessful, FChapterFileListResponseDelegate UserDelegate, FString OriginalPath)
{
    if (!bWasSuccessful || !Response.IsValid() || Response->GetResponseCode() != 200)
    {
        UE_LOG(LogChapterConfigManager, Error, TEXT("Public API: Failed to fetch chapter file list for path '%s'. URL: %s, Code: %d"), *OriginalPath, *Request->GetURL(), Response.IsValid() ? Response->GetResponseCode() : -1);
        UserDelegate.ExecuteIfBound(false, OriginalPath, {}, {});
        return;
    }

    FString JsonString = Response->GetContentAsString();
    TSharedPtr<FJsonObject> JsonObject;
    TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(JsonString);

    TArray<FString> FileNames, FolderNames;
    if (FJsonSerializer::Deserialize(Reader, JsonObject) && JsonObject.IsValid())
    {
        const TArray<TSharedPtr<FJsonValue>>* FilesJsonArray;
        if (JsonObject->TryGetArrayField(TEXT("files"), FilesJsonArray))
        {
            for (const auto& Val : *FilesJsonArray) FileNames.Add(Val->AsString());
        }
        const TArray<TSharedPtr<FJsonValue>>* FoldersJsonArray;
        if (JsonObject->TryGetArrayField(TEXT("folders"), FoldersJsonArray))
        {
            for (const auto& Val : *FoldersJsonArray) FolderNames.Add(Val->AsString());
        }
        UserDelegate.ExecuteIfBound(true, OriginalPath, FileNames, FolderNames);
    }
    else
    {
        UE_LOG(LogChapterConfigManager, Error, TEXT("Public API: Failed to parse chapter file list JSON for path '%s': %s"), *OriginalPath, *JsonString.Left(200));
        UserDelegate.ExecuteIfBound(false, OriginalPath, {}, {});
    }
}

void UChapterConfigManager::FetchAndCacheChapterConfig(const FString& FilePathInRepo, FChapterFileContentResponseDelegate CompletionDelegate)
{
    const UAgentBridgeConfigSettings* Settings = GetSettings();
    if (!Settings || Settings->BackendConfigServiceURL.IsEmpty())
    {
        UE_LOG(LogChapterConfigManager, Error, TEXT("BackendConfigServiceURL is not set for FetchAndCacheChapterConfig."));
        CompletionDelegate.ExecuteIfBound(false, FilePathInRepo, TEXT(""));
        return;
    }

    FString EncodedFilePath = FGenericPlatformHttp::UrlEncode(FilePathInRepo);
    FString Url = FString::Printf(TEXT("%s/api/v1/configs/file?path=%s"), *Settings->BackendConfigServiceURL, *EncodedFilePath);

    TSharedRef<IHttpRequest, ESPMode::ThreadSafe> HttpRequest = FHttpModule::Get().CreateRequest();
    HttpRequest->SetVerb(TEXT("GET"));
    HttpRequest->SetURL(Url);
    HttpRequest->SetHeader(TEXT("Content-Type"), TEXT("application/json"));

    if (!Settings->BackendAPIKey.IsEmpty())
    {
        HttpRequest->SetHeader(TEXT("Authorization"), FString::Printf(TEXT("Bearer %s"), *Settings->BackendAPIKey));
    }

    HttpRequest->OnProcessRequestComplete().BindUObject(this, &UChapterConfigManager::OnPublicChapterFileContentRequestComplete, CompletionDelegate, FilePathInRepo);
    HttpRequest->ProcessRequest();

    UE_LOG(LogChapterConfigManager, Log, TEXT("Fetching chapter config from: %s"), *Url);
}

void UChapterConfigManager::OnPublicChapterFileContentRequestComplete(FHttpRequestPtr Request, FHttpResponsePtr Response, bool bWasSuccessful, FChapterFileContentResponseDelegate UserDelegate, FString OriginalFilePath)
{
    if (bWasSuccessful && Response.IsValid() && Response->GetResponseCode() == 200)
    {
        FString JsonContent = Response->GetContentAsString();
        RawChapterConfigData.Add(OriginalFilePath, JsonContent);
        UE_LOG(LogChapterConfigManager, Log, TEXT("Public API: Successfully fetched and cached chapter config: %s"), *OriginalFilePath);
        UserDelegate.ExecuteIfBound(true, OriginalFilePath, JsonContent);
    }
    else
    {
        UE_LOG(LogChapterConfigManager, Error, TEXT("Public API: Failed to fetch chapter config content for '%s'. URL: %s, Code: %d"), *OriginalFilePath, *Request->GetURL(), Response.IsValid() ? Response->GetResponseCode() : -1);
        UserDelegate.ExecuteIfBound(false, OriginalFilePath, TEXT(""));
    }
}

void UChapterConfigManager::LoadChapterConfigurationsFromLocalRecursive(const FString& RootDirectory, const FString& CurrentRelativePath, int32 CurrentDepth, int32 MaxDepth)
{
    if (CurrentDepth > MaxDepth) return;

    IFileManager& FileManager = IFileManager::Get();
    FString CurrentFullDirectory = FPaths::Combine(RootDirectory, CurrentRelativePath);

    if (!FileManager.DirectoryExists(*CurrentFullDirectory))
    {
        UE_LOG(LogChapterConfigManager, Warning, TEXT("Chapter config directory does not exist: %s"), *CurrentFullDirectory);
        return;
    }

    TArray<FString> FoundFiles;
    FileManager.FindFiles(FoundFiles, *CurrentFullDirectory, TEXT("*.json"));

    for (const FString& FileName : FoundFiles)
    {
        FString FullFilePath = FPaths::Combine(CurrentFullDirectory, FileName);
        FString PathInRepo = CurrentRelativePath + FileName;
        ReadAndCacheLocalChapterFile(FullFilePath, PathInRepo);
    }

    // Recursively process subdirectories if we haven't reached max depth
    if (CurrentDepth < MaxDepth)
    {
        TArray<FString> FoundDirectories;
        FileManager.FindFiles(FoundDirectories, *CurrentFullDirectory, TEXT("*"), false, true);

        for (const FString& DirName : FoundDirectories)
        {
            FString SubRelativePath = CurrentRelativePath + DirName + TEXT("/");
            LoadChapterConfigurationsFromLocalRecursive(RootDirectory, SubRelativePath, CurrentDepth + 1, MaxDepth);
        }
    }
}

bool UChapterConfigManager::ReadAndCacheLocalChapterFile(const FString& FullFilePath, const FString& PathInRepo)
{
    FString JsonString;
    if (!FFileHelper::LoadFileToString(JsonString, *FullFilePath))
    {
        UE_LOG(LogChapterConfigManager, Error, TEXT("Failed to read local chapter config file: %s"), *FullFilePath);
        return false;
    }
    RawChapterConfigData.Add(PathInRepo, JsonString);
    UE_LOG(LogChapterConfigManager, Log, TEXT("Successfully read and cached local chapter config: %s (from %s)"), *PathInRepo, *FullFilePath);
    return true;
}