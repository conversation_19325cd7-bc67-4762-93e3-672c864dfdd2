// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "Subsystems/GameInstanceSubsystem.h"
#include "Interfaces/IHttpRequest.h" // For FHttpRequestPtr, FHttpResponsePtr
#include "Models/TextAdventureModels.h"
#include "ChapterConfigManager.generated.h"

// Forward declarations
class UAgentBridgeConfigSettings;

// Delegates for asynchronous operations
DECLARE_DYNAMIC_DELEGATE_FourParams(FChapterFileListResponseDelegate, bool, bSuccess, const FString&, Path, const TArray<FString>&, FileNames, const TArray<FString>&, FolderNames);
DECLARE_DYNAMIC_DELEGATE_ThreeParams(FChapterFileContentResponseDelegate, bool, bSuccess, const FString&, FilePath, const FString&, JsonContent);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnAllChapterConfigsRequestCompleted, bool, bOverallSuccess); // For recursive loading

UCLASS()
class AGENTBRIDGE_API UChapterConfigManager : public UGameInstanceSubsystem
{
    GENERATED_BODY()

public:
    //~ Begin USubsystem interface
    virtual void Initialize(FSubsystemCollectionBase& Collection) override;
    virtual void Deinitialize() override;
    //~ End USubsystem interface

    /**
     * Attempts to load all chapter configurations starting from the DefaultRootConfigPathInRepo (from settings) or a specified path.
     * This will recursively fetch configurations up to a certain depth.
     * @param OptionalPathInRepo Override the default root path from settings.
     * @param MaxRecursionDepth How many levels of subfolders to explore. 0 means only the given path.
     */
    UFUNCTION(BlueprintCallable, Category = "AgentBridge|ChapterConfiguration")
    void LoadAllChapterConfigurationsRecursive(const FString& OptionalPathInRepo = TEXT(""), int32 MaxRecursionDepth = 1);

    /**
     * Fetches a list of files and folders from a specific path within the GitHub repository (via backend).
     * @param PathInRepo The path within the repository to list.
     * @param ResponseDelegate Delegate to call when the list operation completes.
     */
    UFUNCTION(BlueprintCallable, Category = "AgentBridge|ChapterConfiguration")
    void FetchChapterFileList(const FString& PathInRepo, FChapterFileListResponseDelegate ResponseDelegate);

    /**
     * Fetches and caches a single chapter configuration file from a specific path within the GitHub repository (via backend).
     * @param FilePathInRepo The full path to the configuration file within the repository.
     * @param CompletionDelegate Delegate to call when the fetch operation completes.
     */
    UFUNCTION(BlueprintCallable, Category = "AgentBridge|ChapterConfiguration")
    void FetchAndCacheChapterConfig(const FString& FilePathInRepo, FChapterFileContentResponseDelegate CompletionDelegate);

    /**
     * Generic template function to get a configuration of any type from the cache.
     * @param ConfigFilePathInRepo The path to the configuration file within the repository.
     * @param OutStruct The structure to populate with the parsed configuration data.
     * @return True if the configuration was found and successfully parsed.
     */
    template<typename TStructType>
    bool GetConfiguration(const FString& ConfigFilePathInRepo, TStructType& OutStruct) const;

    /**
     * Gets a specific chapter configuration by file path.
     * @param ConfigFilePathInRepo The path to the chapter configuration file within the repository.
     * @param OutConfig The FChapterConfig structure to populate.
     * @return True if the configuration was found and successfully parsed.
     */
    UFUNCTION(BlueprintCallable, Category = "AgentBridge|ChapterConfiguration", meta=(DisplayName="Get Chapter Config By Path"))
    bool GetChapterConfiguration(const FString& ConfigFilePathInRepo, FChapterConfig& OutConfig) const;

    /**
     * Gets a chapter configuration by Chapter ID.
     * Searches through all cached configurations to find one with the matching ChapterId.
     * @param ChapterId The unique identifier of the chapter to find.
     * @param OutConfig The FChapterConfig structure to populate.
     * @return True if a chapter with the specified ID was found and successfully parsed.
     */
    UFUNCTION(BlueprintCallable, Category = "AgentBridge|ChapterConfiguration", meta=(DisplayName="Get Chapter Config By ID"))
    bool GetChapterConfigurationById(const FString& ChapterId, FChapterConfig& OutConfig) const;

    /**
     * Gets the raw configuration data cache map.
     * This provides direct access to all cached configuration JSON strings.
     * @return A const reference to the internal map of configuration data.
     */
    UFUNCTION(BlueprintCallable, Category = "AgentBridge|ChapterConfiguration", meta=(DisplayName="Get Raw Chapter Config Cache"))
    const TMap<FString, FString>& GetRawChapterConfigCache() const;

    /**
     * Gets a map of parsed FChapterConfig structures.
     * This provides direct access to all cached chapter configurations as parsed objects.
     * Only configurations that can be successfully parsed as FChapterConfig will be included.
     * @return A map of file paths to parsed FChapterConfig structures.
     */
    UFUNCTION(BlueprintCallable, Category = "AgentBridge|ChapterConfiguration", meta=(DisplayName="Get Parsed Chapter Config Cache"))
    TMap<FString, FChapterConfig> GetParsedChapterConfigCache() const;

    /**
     * Gets a list of all available chapter IDs from cached configurations.
     * @return Array of chapter IDs that have been successfully loaded and parsed.
     */
    UFUNCTION(BlueprintCallable, Category = "AgentBridge|ChapterConfiguration", meta=(DisplayName="Get Available Chapter IDs"))
    TArray<FString> GetAvailableChapterIds() const;

    /** Delegate broadcast when LoadAllChapterConfigurationsRecursive completes. */
    UPROPERTY(BlueprintAssignable, Category = "AgentBridge|ChapterConfiguration")
    FOnAllChapterConfigsRequestCompleted OnAllChapterConfigsLoaded;

private:
    // Recursive file list fetching for backend
    void FetchChapterFileListRecursive(const FString& PathInRepo, int32 CurrentDepth, int32 MaxDepth, TSharedRef<int32> InFlightRequests, TSharedRef<bool> OverallSuccessFlag);

    // HTTP response handlers for backend requests
    void OnChapterFileListRequestComplete(FHttpRequestPtr Request, FHttpResponsePtr Response, bool bWasSuccessful, FString PathToList, int32 CurrentDepth, int32 MaxDepth, TSharedRef<int32> InFlightRequests, TSharedRef<bool> OverallSuccessFlag);
    void OnChapterFileContentRequestComplete(FHttpRequestPtr Request, FHttpResponsePtr Response, bool bWasSuccessful, FString OriginalFilePath, TSharedRef<int32> InFlightRequests, TSharedRef<bool> OverallSuccessFlag);

    // For single file/list requests via public API
    void OnPublicChapterFileListRequestComplete(FHttpRequestPtr Request, FHttpResponsePtr Response, bool bWasSuccessful, FChapterFileListResponseDelegate UserDelegate, FString OriginalPath);
    void OnPublicChapterFileContentRequestComplete(FHttpRequestPtr Request, FHttpResponsePtr Response, bool bWasSuccessful, FChapterFileContentResponseDelegate UserDelegate, FString OriginalFilePath);

    // Local file operations
    void LoadChapterConfigurationsFromLocalRecursive(const FString& RootDirectory, const FString& CurrentRelativePath, int32 CurrentDepth, int32 MaxDepth);
    bool ReadAndCacheLocalChapterFile(const FString& FullFilePath, const FString& PathInRepo);

    // Common parsing logic
    template<typename TStructType>
    bool ParseChapterConfigData(const FString& JsonString, const FString& ConfigName, TStructType& OutStruct) const;

    // Stores raw JSON string data for each config, keyed by full path in repo
    TMap<FString, FString> RawChapterConfigData;

    const UAgentBridgeConfigSettings* GetSettings() const;
};

// Template implementation for GetConfiguration
template<typename TStructType>
bool UChapterConfigManager::GetConfiguration(const FString& ConfigFilePathInRepo, TStructType& OutStruct) const
{
    const FString* JsonString = RawChapterConfigData.Find(ConfigFilePathInRepo);
    if (JsonString)
    {
        return ParseChapterConfigData(*JsonString, ConfigFilePathInRepo, OutStruct);
    }
    UE_LOG(LogTemp, Warning, TEXT("Chapter configuration '%s' not found in cache."), *ConfigFilePathInRepo);
    return false;
}

// Template implementation for ParseChapterConfigData
template<typename TStructType>
bool UChapterConfigManager::ParseChapterConfigData(const FString& JsonString, const FString& ConfigName, TStructType& OutStruct) const
{
    if (!FJsonObjectConverter::JsonObjectStringToUStruct(JsonString, &OutStruct, 0, 0))
    {
        UE_LOG(LogTemp, Error, TEXT("Failed to parse JSON for chapter config '%s'. JSON: %s"), *ConfigName, *JsonString.Left(500));
        return false;
    }
    return true;
}
